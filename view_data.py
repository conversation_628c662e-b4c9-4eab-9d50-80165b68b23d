#!/usr/bin/env python3
"""
数据查看脚本
用于查看数据库中的医院数据
"""

import json
from database import DatabaseManager, DATABASE_CONFIG
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def display_hospital_stats(db_manager):
    """显示医院数据统计"""
    try:
        with db_manager.connection.cursor() as cursor:
            # 总数统计
            cursor.execute("SELECT COUNT(*) FROM hospitals")
            total_count = cursor.fetchone()[0]
            
            # 有电话的医院数量
            cursor.execute("SELECT COUNT(*) FROM hospitals WHERE phone != ''")
            with_phone_count = cursor.fetchone()[0]
            
            # 有地址的医院数量
            cursor.execute("SELECT COUNT(*) FROM hospitals WHERE address != ''")
            with_address_count = cursor.fetchone()[0]
            
            # 有标签的医院数量
            cursor.execute("SELECT COUNT(*) FROM hospitals WHERE tags IS NOT NULL AND JSON_LENGTH(tags) > 0")
            with_tags_count = cursor.fetchone()[0]
            
            print("\n" + "="*60)
            print("医院数据统计")
            print("="*60)
            print(f"总医院数量: {total_count}")
            print(f"有电话的医院: {with_phone_count} ({with_phone_count/total_count*100:.1f}%)")
            print(f"有地址的医院: {with_address_count} ({with_address_count/total_count*100:.1f}%)")
            print(f"有标签的医院: {with_tags_count} ({with_tags_count/total_count*100:.1f}%)")
            print("="*60)
            
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")


def display_recent_hospitals(db_manager, limit=10):
    """显示最近添加的医院"""
    hospitals = db_manager.get_hospitals(limit=limit)
    
    if not hospitals:
        print("没有找到医院数据")
        return
    
    print(f"\n最近添加的 {len(hospitals)} 个医院:")
    print("-" * 100)
    
    for i, hospital in enumerate(hospitals, 1):
        print(f"\n{i}. 【{hospital['name']}】")
        if hospital['alias']:
            print(f"   别名: {hospital['alias']}")
        if hospital['tags']:
            tags_str = ', '.join(hospital['tags'])
            print(f"   标签: {tags_str}")
        if hospital['phone']:
            print(f"   电话: {hospital['phone']}")
        if hospital['address']:
            print(f"   地址: {hospital['address']}")
        if hospital['remark']:
            print(f"   备注: {hospital['remark']}")
        print(f"   创建时间: {hospital['created_at']}")


def search_hospitals_by_name(db_manager, keyword):
    """根据名称搜索医院"""
    try:
        search_sql = """
        SELECT id, name, alias, tags, remark, phone, address, created_at
        FROM hospitals
        WHERE name LIKE %s OR alias LIKE %s
        ORDER BY created_at DESC
        LIMIT 20
        """
        
        search_pattern = f"%{keyword}%"
        
        with db_manager.connection.cursor() as cursor:
            cursor.execute(search_sql, (search_pattern, search_pattern))
            results = cursor.fetchall()
            
            if not results:
                print(f"没有找到包含 '{keyword}' 的医院")
                return
            
            print(f"\n找到 {len(results)} 个包含 '{keyword}' 的医院:")
            print("-" * 100)
            
            for i, row in enumerate(results, 1):
                print(f"\n{i}. 【{row[1]}】")  # name
                if row[2]:  # alias
                    print(f"   别名: {row[2]}")
                if row[3]:  # tags
                    try:
                        tags = json.loads(row[3])
                        tags_str = ', '.join(tags)
                        print(f"   标签: {tags_str}")
                    except:
                        pass
                if row[5]:  # phone
                    print(f"   电话: {row[5]}")
                if row[6]:  # address
                    print(f"   地址: {row[6]}")
                if row[4]:  # remark
                    print(f"   备注: {row[4]}")
                print(f"   创建时间: {row[7]}")
                
    except Exception as e:
        logger.error(f"搜索医院失败: {str(e)}")


def display_tag_statistics(db_manager):
    """显示标签统计"""
    try:
        with db_manager.connection.cursor() as cursor:
            cursor.execute("""
                SELECT tags FROM hospitals 
                WHERE tags IS NOT NULL AND JSON_LENGTH(tags) > 0
            """)
            results = cursor.fetchall()
            
            tag_count = {}
            for row in results:
                try:
                    tags = json.loads(row[0])
                    for tag in tags:
                        tag_count[tag] = tag_count.get(tag, 0) + 1
                except:
                    continue
            
            if tag_count:
                print("\n标签统计 (前20个):")
                print("-" * 50)
                sorted_tags = sorted(tag_count.items(), key=lambda x: x[1], reverse=True)
                for i, (tag, count) in enumerate(sorted_tags[:20], 1):
                    print(f"{i:2d}. {tag}: {count}")
            else:
                print("没有找到标签数据")
                
    except Exception as e:
        logger.error(f"获取标签统计失败: {str(e)}")


def main():
    """主函数"""
    db_manager = DatabaseManager(**DATABASE_CONFIG)
    
    if not db_manager.connect():
        logger.error("无法连接到数据库")
        return
    
    try:
        while True:
            print("\n" + "="*60)
            print("医院数据查看工具")
            print("="*60)
            print("1. 显示数据统计")
            print("2. 显示最近添加的医院")
            print("3. 搜索医院")
            print("4. 显示标签统计")
            print("5. 退出")
            print("-"*60)
            
            choice = input("请选择操作 (1-5): ").strip()
            
            if choice == '1':
                display_hospital_stats(db_manager)
            elif choice == '2':
                limit = input("显示数量 (默认10): ").strip()
                limit = int(limit) if limit.isdigit() else 10
                display_recent_hospitals(db_manager, limit)
            elif choice == '3':
                keyword = input("请输入搜索关键词: ").strip()
                if keyword:
                    search_hospitals_by_name(db_manager, keyword)
                else:
                    print("请输入有效的关键词")
            elif choice == '4':
                display_tag_statistics(db_manager)
            elif choice == '5':
                print("再见！")
                break
            else:
                print("无效的选择，请重新输入")
                
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")
    finally:
        db_manager.disconnect()


if __name__ == "__main__":
    main()
