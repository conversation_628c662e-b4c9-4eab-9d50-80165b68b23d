import pymysql
import json
from typing import List, Dict, Optional
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理类"""
    
    def __init__(self, host: str, port: int, user: str, password: str, database: str, charset: str = 'utf8mb4'):
        """
        初始化数据库连接参数
        
        Args:
            host: 数据库主机地址
            port: 数据库端口
            user: 数据库用户名
            password: 数据库密码
            database: 数据库名称
            charset: 字符集，默认utf8mb4
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.charset = charset
        self.connection = None
    
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset=self.charset,
                autocommit=True
            )
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return False
    
    def disconnect(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")
    
    def create_hospital_table(self):
        """创建医院数据表"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS hospitals (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            name VARCHAR(255) NOT NULL COMMENT '医院名称',
            alias VARCHAR(255) DEFAULT '' COMMENT '医院别名',
            tags JSON COMMENT '医院标签（JSON格式）',
            remark TEXT COMMENT '医院介绍/备注',
            phone VARCHAR(100) DEFAULT '' COMMENT '联系电话',
            address TEXT COMMENT '医院地址',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_name (name),
            INDEX idx_phone (phone),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医院信息表';
        """
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(create_table_sql)
            logger.info("医院数据表创建成功")
            return True
        except Exception as e:
            logger.error(f"创建数据表失败: {str(e)}")
            return False
    
    def insert_hospital(self, hospital_data: Dict) -> bool:
        """
        插入单条医院数据
        
        Args:
            hospital_data: 医院数据字典
            
        Returns:
            bool: 插入是否成功
        """
        insert_sql = """
        INSERT INTO hospitals (name, alias, tags, remark, phone, address)
        VALUES (%s, %s, %s, %s, %s, %s)
        """
        
        try:
            # 处理tags字段，转换为JSON字符串
            tags_json = json.dumps(hospital_data.get('tags', []), ensure_ascii=False)
            
            values = (
                hospital_data.get('name', ''),
                hospital_data.get('alias', ''),
                tags_json,
                hospital_data.get('remark', ''),
                hospital_data.get('phone', ''),
                hospital_data.get('address', '')
            )
            
            with self.connection.cursor() as cursor:
                cursor.execute(insert_sql, values)
            
            logger.info(f"成功插入医院数据: {hospital_data.get('name', 'Unknown')}")
            return True
            
        except Exception as e:
            logger.error(f"插入医院数据失败: {str(e)}")
            logger.error(f"数据内容: {hospital_data}")
            return False
    
    def batch_insert_hospitals(self, hospitals_data: List[Dict]) -> int:
        """
        批量插入医院数据
        
        Args:
            hospitals_data: 医院数据列表
            
        Returns:
            int: 成功插入的记录数
        """
        insert_sql = """
        INSERT INTO hospitals (name, alias, tags, remark, phone, address)
        VALUES (%s, %s, %s, %s, %s, %s)
        """
        
        success_count = 0
        
        try:
            with self.connection.cursor() as cursor:
                for hospital_data in hospitals_data:
                    try:
                        # 处理tags字段，转换为JSON字符串
                        tags_json = json.dumps(hospital_data.get('tags', []), ensure_ascii=False)
                        
                        values = (
                            hospital_data.get('name', ''),
                            hospital_data.get('alias', ''),
                            tags_json,
                            hospital_data.get('remark', ''),
                            hospital_data.get('phone', ''),
                            hospital_data.get('address', '')
                        )
                        
                        cursor.execute(insert_sql, values)
                        success_count += 1
                        
                    except Exception as e:
                        logger.error(f"插入单条数据失败: {str(e)}")
                        logger.error(f"数据内容: {hospital_data}")
                        continue
            
            logger.info(f"批量插入完成，成功插入 {success_count} 条记录")
            return success_count
            
        except Exception as e:
            logger.error(f"批量插入失败: {str(e)}")
            return success_count
    
    def check_hospital_exists(self, name: str, phone: str = None) -> bool:
        """
        检查医院是否已存在
        
        Args:
            name: 医院名称
            phone: 联系电话（可选）
            
        Returns:
            bool: 是否存在
        """
        if phone:
            check_sql = "SELECT COUNT(*) FROM hospitals WHERE name = %s AND phone = %s"
            values = (name, phone)
        else:
            check_sql = "SELECT COUNT(*) FROM hospitals WHERE name = %s"
            values = (name,)
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(check_sql, values)
                count = cursor.fetchone()[0]
                return count > 0
        except Exception as e:
            logger.error(f"检查医院是否存在时出错: {str(e)}")
            return False
    
    def get_hospital_count(self) -> int:
        """获取医院总数"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) FROM hospitals")
                count = cursor.fetchone()[0]
                return count
        except Exception as e:
            logger.error(f"获取医院总数失败: {str(e)}")
            return 0
    
    def get_hospitals(self, limit: int = 10, offset: int = 0) -> List[Dict]:
        """
        获取医院列表
        
        Args:
            limit: 限制返回数量
            offset: 偏移量
            
        Returns:
            List[Dict]: 医院数据列表
        """
        select_sql = """
        SELECT id, name, alias, tags, remark, phone, address, created_at, updated_at
        FROM hospitals
        ORDER BY created_at DESC
        LIMIT %s OFFSET %s
        """
        
        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(select_sql, (limit, offset))
                results = cursor.fetchall()
                
                # 处理JSON字段
                for result in results:
                    if result['tags']:
                        try:
                            result['tags'] = json.loads(result['tags'])
                        except:
                            result['tags'] = []
                
                return results
        except Exception as e:
            logger.error(f"获取医院列表失败: {str(e)}")
            return []


# 数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'your_password',  # 请修改为你的数据库密码
    'database': 'kangfu_db',      # 请修改为你的数据库名称
    'charset': 'utf8mb4'
}


def init_database():
    """初始化数据库"""
    db = DatabaseManager(**DATABASE_CONFIG)
    
    if db.connect():
        # 创建表
        if db.create_hospital_table():
            logger.info("数据库初始化完成")
        else:
            logger.error("数据库初始化失败")
        db.disconnect()
    else:
        logger.error("无法连接到数据库")


if __name__ == "__main__":
    # 初始化数据库
    init_database()
