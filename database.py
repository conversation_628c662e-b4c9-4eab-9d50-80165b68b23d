import pymysql
import json
from typing import List, Dict, Optional
from datetime import datetime
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理类"""
    
    def __init__(self, host: str, port: int, user: str, password: str, database: str, charset: str = 'utf8mb4'):
        """
        初始化数据库连接参数
        
        Args:
            host: 数据库主机地址
            port: 数据库端口
            user: 数据库用户名
            password: 数据库密码
            database: 数据库名称
            charset: 字符集，默认utf8mb4
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.charset = charset
        self.connection = None
    
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset=self.charset,
                autocommit=True
            )
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return False
    
    def disconnect(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")
    
    def create_hospital_table(self):
        """创建医院数据表（使用用户提供的表结构）"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS `hospital` (
            `id` BIGINT NOT NULL COMMENT 'ID',
            `name` VARCHAR (100) NOT NULL COMMENT '名称',
            `alias` VARCHAR (50) NULL DEFAULT NULL COMMENT '别名',
            `remark` TEXT DEFAULT NULL COMMENT '介绍',
            `phone` VARCHAR (50) NULL DEFAULT NULL COMMENT '电话',
            `address` VARCHAR (150) NULL DEFAULT NULL COMMENT '地址',
            `sort` INT NOT NULL DEFAULT '0' COMMENT '排序',
            `hidden` TINYINT NOT NULL DEFAULT '0' COMMENT '是否隐藏（0、否；1、是）',
            `longitude` DOUBLE NOT NULL DEFAULT 0 COMMENT '经度',
            `latitude` DOUBLE NOT NULL DEFAULT 0 COMMENT '纬度',
            `tags` VARCHAR (200) NULL DEFAULT NULL COMMENT '标签（多个逗号分隔）',
            `create_time` DATETIME NOT NULL COMMENT '创建时间',
            `create_user` BIGINT NOT NULL COMMENT '创建用户',
            `update_time` DATETIME NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
            `update_user` BIGINT NOT NULL DEFAULT '0' COMMENT '修改用户',
            `deleted` INT NOT NULL DEFAULT '0' COMMENT '逻辑删除（0代表未删除，非0代表已删除）',
            `version` INT NOT NULL DEFAULT '0' COMMENT '更新版本号（乐观锁）',
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医院信息';
        """

        try:
            with self.connection.cursor() as cursor:
                cursor.execute(create_table_sql)
            logger.info("医院数据表创建成功")
            return True
        except Exception as e:
            logger.error(f"创建数据表失败: {str(e)}")
            return False
    
    def generate_id(self) -> int:
        """生成唯一ID（使用时间戳 + 随机数）"""
        return int(time.time() * 1000000) + int(time.time() * 1000) % 1000

    def insert_hospital(self, hospital_data: Dict) -> bool:
        """
        插入单条医院数据

        Args:
            hospital_data: 医院数据字典

        Returns:
            bool: 插入是否成功
        """
        insert_sql = """
        INSERT INTO hospital (id, name, alias, remark, phone, address, tags, create_time, create_user, update_time, update_user)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        try:
            # 处理tags字段，转换为逗号分隔的字符串
            tags_list = hospital_data.get('tags', [])
            tags_str = ','.join(tags_list) if tags_list else None

            # 生成ID和时间
            hospital_id = self.generate_id()
            current_time = datetime.now()

            values = (
                hospital_id,
                hospital_data.get('name', ''),
                hospital_data.get('alias', '') or None,
                hospital_data.get('remark', '') or None,
                hospital_data.get('phone', '') or None,
                hospital_data.get('address', '') or None,
                tags_str,
                current_time,
                1,  # create_user 默认为1
                current_time,
                1   # update_user 默认为1
            )

            with self.connection.cursor() as cursor:
                cursor.execute(insert_sql, values)

            logger.info(f"成功插入医院数据: {hospital_data.get('name', 'Unknown')} (ID: {hospital_id})")
            return True

        except Exception as e:
            logger.error(f"插入医院数据失败: {str(e)}")
            logger.error(f"数据内容: {hospital_data}")
            return False
    
    def batch_insert_hospitals(self, hospitals_data: List[Dict]) -> int:
        """
        批量插入医院数据

        Args:
            hospitals_data: 医院数据列表

        Returns:
            int: 成功插入的记录数
        """
        insert_sql = """
        INSERT INTO hospital (id, name, alias, remark, phone, address, tags, create_time, create_user, update_time, update_user)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        success_count = 0

        try:
            with self.connection.cursor() as cursor:
                for hospital_data in hospitals_data:
                    try:
                        # 处理tags字段，转换为逗号分隔的字符串
                        tags_list = hospital_data.get('tags', [])
                        tags_str = ','.join(tags_list) if tags_list else None

                        # 生成ID和时间
                        hospital_id = self.generate_id()
                        current_time = datetime.now()

                        values = (
                            hospital_id,
                            hospital_data.get('name', ''),
                            hospital_data.get('alias', '') or None,
                            hospital_data.get('remark', '') or None,
                            hospital_data.get('phone', '') or None,
                            hospital_data.get('address', '') or None,
                            tags_str,
                            current_time,
                            1,  # create_user 默认为1
                            current_time,
                            1   # update_user 默认为1
                        )

                        cursor.execute(insert_sql, values)
                        success_count += 1

                    except Exception as e:
                        logger.error(f"插入单条数据失败: {str(e)}")
                        logger.error(f"数据内容: {hospital_data}")
                        continue

            logger.info(f"批量插入完成，成功插入 {success_count} 条记录")
            return success_count

        except Exception as e:
            logger.error(f"批量插入失败: {str(e)}")
            return success_count
    
    def check_hospital_exists(self, name: str, phone: str = None) -> bool:
        """
        检查医院是否已存在（排除逻辑删除的记录）

        Args:
            name: 医院名称
            phone: 联系电话（可选）

        Returns:
            bool: 是否存在
        """
        if phone:
            check_sql = "SELECT COUNT(*) FROM hospital WHERE name = %s AND phone = %s AND deleted = 0"
            values = (name, phone)
        else:
            check_sql = "SELECT COUNT(*) FROM hospital WHERE name = %s AND deleted = 0"
            values = (name,)

        try:
            with self.connection.cursor() as cursor:
                cursor.execute(check_sql, values)
                count = cursor.fetchone()[0]
                return count > 0
        except Exception as e:
            logger.error(f"检查医院是否存在时出错: {str(e)}")
            return False
    
    def get_hospital_count(self) -> int:
        """获取医院总数（排除逻辑删除的记录）"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) FROM hospital WHERE deleted = 0")
                count = cursor.fetchone()[0]
                return count
        except Exception as e:
            logger.error(f"获取医院总数失败: {str(e)}")
            return 0
    
    def get_hospitals(self, limit: int = 10, offset: int = 0) -> List[Dict]:
        """
        获取医院列表（排除逻辑删除的记录）

        Args:
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            List[Dict]: 医院数据列表
        """
        select_sql = """
        SELECT id, name, alias, tags, remark, phone, address, create_time, update_time
        FROM hospital
        WHERE deleted = 0
        ORDER BY create_time DESC
        LIMIT %s OFFSET %s
        """

        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(select_sql, (limit, offset))
                results = cursor.fetchall()

                # 处理tags字段（从逗号分隔的字符串转换为列表）
                for result in results:
                    if result['tags']:
                        result['tags'] = result['tags'].split(',')
                    else:
                        result['tags'] = []

                return results
        except Exception as e:
            logger.error(f"获取医院列表失败: {str(e)}")
            return []


# 从config.py导入数据库配置
from config import DATABASE_CONFIG


def init_database():
    """初始化数据库"""
    db = DatabaseManager(**DATABASE_CONFIG)
    
    if db.connect():
        # 创建表
        if db.create_hospital_table():
            logger.info("数据库初始化完成")
        else:
            logger.error("数据库初始化失败")
        db.disconnect()
    else:
        logger.error("无法连接到数据库")


if __name__ == "__main__":
    # 初始化数据库
    init_database()
