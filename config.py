# 数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',        # 数据库主机地址
    'port': 3306,              # 数据库端口
    'user': 'root',            # 数据库用户名
    'password': 'root',        # 数据库密码
    'database': 'kangfu',      # 数据库名称
    'charset': 'utf8mb4'       # 字符集
}

# 爬虫配置
SPIDER_CONFIG = {
    'base_url': "https://so.99.com.cn/search.php?s=relevance&proj=yyk&f=_all&q=康复医院&p=1",
    'headers': {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    },
    'request_delay': 1,        # 请求间隔（秒）
    'page_delay': 2,          # 页面间隔（秒）
    'start_page': 1,          # 开始页面
    'end_page': 60,           # 结束页面
    'timeout': 30             # 请求超时时间（秒）
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'filename': 'spider.log'  # 日志文件名，如果为None则输出到控制台
}
