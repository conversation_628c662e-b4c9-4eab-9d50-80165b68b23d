#!/usr/bin/env python3
"""
Peewee ORM Demo
轻量级Python ORM框架，简单易用，API直观
"""

from peewee import *
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库连接
database = MySQLDatabase(
    'kangfu',
    user='root',
    password='root',
    host='localhost',
    port=3306,
    charset='utf8mb4'
)


class BaseModel(Model):
    """基础模型类"""
    class Meta:
        database = database


class Hospital(BaseModel):
    """医院模型类"""
    id = BigIntegerField(primary_key=True)
    name = CharField(max_length=100, null=False)
    alias = CharField(max_length=50, null=True)
    remark = TextField(null=True)
    phone = CharField(max_length=50, null=True)
    address = CharField(max_length=150, null=True)
    sort = IntegerField(default=0)
    hidden = IntegerField(default=0)
    longitude = DoubleField(default=0)
    latitude = DoubleField(default=0)
    tags = CharField(max_length=200, null=True)
    create_time = DateTimeField(default=datetime.now)
    create_user = BigIntegerField(default=1)
    update_time = DateTimeField(default=datetime.now)
    update_user = BigIntegerField(default=1)
    deleted = IntegerField(default=0)
    version = IntegerField(default=0)
    
    class Meta:
        table_name = 'hospital'
    
    def __str__(self):
        return f"Hospital(id={self.id}, name='{self.name}', phone='{self.phone}')"


class HospitalService:
    """医院服务类 - 封装CRUD操作"""
    
    @staticmethod
    def create_hospital(hospital_data: dict) -> Hospital:
        """创建医院"""
        # 处理tags
        tags_str = ','.join(hospital_data.get('tags', [])) if hospital_data.get('tags') else None
        
        hospital = Hospital.create(
            id=int(datetime.now().timestamp() * 1000000),  # 生成ID
            name=hospital_data.get('name', ''),
            alias=hospital_data.get('alias'),
            remark=hospital_data.get('remark'),
            phone=hospital_data.get('phone'),
            address=hospital_data.get('address'),
            tags=tags_str
        )
        
        logger.info(f"创建医院成功: {hospital.name} (ID: {hospital.id})")
        return hospital
    
    @staticmethod
    def get_hospital_by_id(hospital_id: int) -> Hospital:
        """根据ID获取医院"""
        try:
            hospital = Hospital.get(
                (Hospital.id == hospital_id) & 
                (Hospital.deleted == 0)
            )
            return hospital
        except Hospital.DoesNotExist:
            return None
    
    @staticmethod
    def get_hospitals(limit: int = 10, offset: int = 0) -> list:
        """分页获取医院列表"""
        hospitals = (Hospital
                    .select()
                    .where(Hospital.deleted == 0)
                    .order_by(Hospital.create_time.desc())
                    .limit(limit)
                    .offset(offset))
        return list(hospitals)
    
    @staticmethod
    def search_hospitals(keyword: str) -> list:
        """搜索医院"""
        hospitals = (Hospital
                    .select()
                    .where(
                        ((Hospital.name.contains(keyword)) | 
                         (Hospital.alias.contains(keyword))) &
                        (Hospital.deleted == 0)
                    ))
        return list(hospitals)
    
    @staticmethod
    def update_hospital(hospital_id: int, update_data: dict) -> Hospital:
        """更新医院信息"""
        hospital = HospitalService.get_hospital_by_id(hospital_id)
        if not hospital:
            return None
        
        # 处理tags
        if 'tags' in update_data and isinstance(update_data['tags'], list):
            update_data['tags'] = ','.join(update_data['tags']) if update_data['tags'] else None
        
        # 添加更新时间
        update_data['update_time'] = datetime.now()
        
        # 执行更新
        Hospital.update(**update_data).where(Hospital.id == hospital_id).execute()
        
        # 重新获取更新后的对象
        updated_hospital = HospitalService.get_hospital_by_id(hospital_id)
        logger.info(f"更新医院成功: {updated_hospital.name}")
        return updated_hospital
    
    @staticmethod
    def delete_hospital(hospital_id: int) -> bool:
        """逻辑删除医院"""
        hospital = HospitalService.get_hospital_by_id(hospital_id)
        if not hospital:
            return False
        
        Hospital.update(
            deleted=1,
            update_time=datetime.now()
        ).where(Hospital.id == hospital_id).execute()
        
        logger.info(f"删除医院成功: {hospital.name}")
        return True
    
    @staticmethod
    def check_hospital_exists(name: str, phone: str = None) -> bool:
        """检查医院是否存在"""
        query = Hospital.select().where(
            (Hospital.name == name) & 
            (Hospital.deleted == 0)
        )
        
        if phone:
            query = query.where(Hospital.phone == phone)
        
        return query.exists()
    
    @staticmethod
    def get_hospital_count() -> int:
        """获取医院总数"""
        return Hospital.select().where(Hospital.deleted == 0).count()


def demo_peewee():
    """Peewee CRUD 演示"""
    logger.info("=== Peewee ORM Demo ===")
    
    # 连接数据库
    database.connect()
    
    try:
        # 1. 创建医院
        logger.info("1. 创建医院")
        hospital_data = {
            'name': 'Peewee康复医院',
            'alias': 'PW医院',
            'remark': '使用Peewee ORM创建的医院',
            'phone': '010-22222222',
            'address': '北京市朝阳区Peewee大街456号',
            'tags': ['康复科', 'Peewee', 'ORM']
        }
        
        if not HospitalService.check_hospital_exists(hospital_data['name'], hospital_data['phone']):
            hospital = HospitalService.create_hospital(hospital_data)
            logger.info(f"创建的医院: {hospital}")
        else:
            logger.info("医院已存在，跳过创建")
        
        # 2. 查询医院
        logger.info("\n2. 查询医院")
        hospitals = HospitalService.get_hospitals(limit=3)
        logger.info(f"查询到 {len(hospitals)} 个医院:")
        for h in hospitals:
            tags = h.tags.split(',') if h.tags else []
            logger.info(f"  - {h.name} | {h.phone} | 标签: {tags}")
        
        # 3. 搜索医院
        logger.info("\n3. 搜索医院")
        search_results = HospitalService.search_hospitals('Peewee')
        logger.info(f"搜索'Peewee'结果: {len(search_results)} 个")
        for h in search_results:
            logger.info(f"  - {h.name} | {h.phone}")
        
        # 4. 更新医院
        logger.info("\n4. 更新医院")
        if search_results:
            hospital = search_results[0]
            update_data = {
                'remark': '已更新的医院介绍 - Peewee很简单！',
                'tags': ['康复科', 'Peewee', 'ORM', '已更新']
            }
            updated_hospital = HospitalService.update_hospital(hospital.id, update_data)
            if updated_hospital:
                logger.info(f"更新成功: {updated_hospital.remark}")
        
        # 5. 统计信息
        logger.info("\n5. 统计信息")
        total_count = HospitalService.get_hospital_count()
        logger.info(f"数据库中总计 {total_count} 个医院")
        
        logger.info("\nPeewee Demo 完成!")
        
    finally:
        database.close()


if __name__ == "__main__":
    demo_peewee()
