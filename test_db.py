#!/usr/bin/env python3
"""
数据库连接测试脚本
用于测试数据库连接和插入功能
"""

from database import DatabaseManager, DATABASE_CONFIG
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_database_connection():
    """测试数据库连接"""
    logger.info("开始测试数据库连接...")
    
    db_manager = DatabaseManager(**DATABASE_CONFIG)
    
    if not db_manager.connect():
        logger.error("数据库连接失败")
        return False
    
    try:
        # 测试查询
        with db_manager.connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            logger.info(f"MySQL版本: {version[0]}")
            
            cursor.execute("SELECT DATABASE()")
            database = cursor.fetchone()
            logger.info(f"当前数据库: {database[0]}")
            
            # 检查表是否存在
            cursor.execute("SHOW TABLES LIKE 'hospital'")
            table_exists = cursor.fetchone()
            if table_exists:
                logger.info("hospital表已存在")
                
                # 查看表结构
                cursor.execute("DESCRIBE hospital")
                columns = cursor.fetchall()
                logger.info("表结构:")
                for column in columns:
                    logger.info(f"  {column[0]} - {column[1]} - {column[2]}")
            else:
                logger.warning("hospital表不存在，请先运行 init_db.py")
        
        logger.info("数据库连接测试成功")
        return True
        
    except Exception as e:
        logger.error(f"数据库测试失败: {str(e)}")
        return False
    finally:
        db_manager.disconnect()


def test_insert_sample_data():
    """测试插入示例数据"""
    logger.info("开始测试插入示例数据...")
    
    db_manager = DatabaseManager(**DATABASE_CONFIG)
    
    if not db_manager.connect():
        logger.error("数据库连接失败")
        return False
    
    try:
        
        # 示例数据
        sample_data = {
            'name': '测试康复医院',
            'alias': '测试医院',
            'remark': '这是一个测试医院数据',
            'phone': '010-12345678',
            'address': '北京市朝阳区测试街道123号',
            'tags': ['康复科', '物理治疗', '测试标签']
        }
        
        # 检查是否已存在
        if db_manager.check_hospital_exists(sample_data['name'], sample_data['phone']):
            logger.info("测试数据已存在，跳过插入")
        else:
            # 插入数据
            if db_manager.insert_hospital(sample_data):
                logger.info("测试数据插入成功")
            else:
                logger.error("测试数据插入失败")
                return False
        
        # 查询数据验证
        hospitals = db_manager.get_hospitals(limit=5)
        logger.info(f"查询到 {len(hospitals)} 条医院数据")
        
        for hospital in hospitals:
            logger.info(f"医院: {hospital['name']} - {hospital['phone']}")
        
        # 统计信息
        total_count = db_manager.get_hospital_count()
        logger.info(f"数据库中总计 {total_count} 条医院记录")
        
        logger.info("数据插入测试成功")
        return True
        
    except Exception as e:
        logger.error(f"数据插入测试失败: {str(e)}")
        return False
    finally:
        db_manager.disconnect()


def main():
    """主函数"""
    logger.info("="*60)
    logger.info("数据库测试开始")
    logger.info("="*60)
    
    # 测试连接
    if not test_database_connection():
        logger.error("数据库连接测试失败，程序退出")
        return
    
    # 测试插入
    if not test_insert_sample_data():
        logger.error("数据插入测试失败，程序退出")
        return
    
    logger.info("="*60)
    logger.info("所有测试通过！数据库配置正确")
    logger.info("现在可以运行爬虫脚本了：python spider_with_db.py")
    logger.info("="*60)


if __name__ == "__main__":
    main()
