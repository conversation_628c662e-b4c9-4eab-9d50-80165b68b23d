#!/usr/bin/env python3
"""
数据库基础操作示例
演示如何使用DatabaseManager进行增删改查操作
"""

from database import DatabaseManager, DATABASE_CONFIG
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def demo_insert():
    """演示插入数据"""
    logger.info("=== 演示插入数据 ===")
    
    db = DatabaseManager(**DATABASE_CONFIG)
    if not db.connect():
        return
    
    try:
        # 示例医院数据
        hospital_data = {
            'name': '北京康复医院',
            'alias': '北康医院',
            'remark': '专业的康复医疗机构',
            'phone': '010-88888888',
            'address': '北京市朝阳区康复路123号',
            'tags': ['康复科', '物理治疗', '神经康复']
        }
        
        # 检查是否已存在
        if db.check_hospital_exists(hospital_data['name'], hospital_data['phone']):
            logger.info("医院已存在，跳过插入")
        else:
            if db.insert_hospital(hospital_data):
                logger.info("医院数据插入成功")
            else:
                logger.error("医院数据插入失败")
                
    finally:
        db.disconnect()


def demo_batch_insert():
    """演示批量插入数据"""
    logger.info("=== 演示批量插入数据 ===")
    
    db = DatabaseManager(**DATABASE_CONFIG)
    if not db.connect():
        return
    
    try:
        # 示例医院数据列表
        hospitals_data = [
            {
                'name': '上海康复中心',
                'alias': '上康中心',
                'remark': '综合性康复医疗中心',
                'phone': '021-66666666',
                'address': '上海市浦东新区康复大道456号',
                'tags': ['康复科', '中医康复', '运动康复']
            },
            {
                'name': '广州康复医院',
                'alias': '广康医院',
                'remark': '华南地区知名康复医院',
                'phone': '020-77777777',
                'address': '广州市天河区康复街789号',
                'tags': ['康复科', '儿童康复', '老年康复']
            }
        ]
        
        success_count = db.batch_insert_hospitals(hospitals_data)
        logger.info(f"批量插入完成，成功插入 {success_count} 条记录")
        
    finally:
        db.disconnect()


def demo_query():
    """演示查询数据"""
    logger.info("=== 演示查询数据 ===")
    
    db = DatabaseManager(**DATABASE_CONFIG)
    if not db.connect():
        return
    
    try:
        # 获取医院总数
        total_count = db.get_hospital_count()
        logger.info(f"数据库中总计 {total_count} 条医院记录")
        
        # 获取最新的5条记录
        hospitals = db.get_hospitals(limit=5)
        logger.info(f"最新的 {len(hospitals)} 条医院记录:")
        
        for i, hospital in enumerate(hospitals, 1):
            logger.info(f"{i}. {hospital['name']} - {hospital['phone']}")
            if hospital['tags']:
                logger.info(f"   标签: {', '.join(hospital['tags'])}")
            logger.info(f"   地址: {hospital['address']}")
            logger.info(f"   创建时间: {hospital['create_time']}")
            logger.info("-" * 50)
            
    finally:
        db.disconnect()


def demo_search():
    """演示搜索功能"""
    logger.info("=== 演示搜索功能 ===")
    
    db = DatabaseManager(**DATABASE_CONFIG)
    if not db.connect():
        return
    
    try:
        # 搜索包含"康复"的医院
        search_keyword = "康复"
        
        search_sql = """
        SELECT id, name, alias, tags, remark, phone, address, create_time
        FROM hospital
        WHERE (name LIKE %s OR alias LIKE %s) AND deleted = 0
        ORDER BY create_time DESC
        LIMIT 10
        """
        
        search_pattern = f"%{search_keyword}%"
        
        with db.connection.cursor() as cursor:
            cursor.execute(search_sql, (search_pattern, search_pattern))
            results = cursor.fetchall()
            
            logger.info(f"找到 {len(results)} 个包含 '{search_keyword}' 的医院:")
            
            for i, row in enumerate(results, 1):
                logger.info(f"{i}. {row[1]} - {row[5]}")  # name - phone
                if row[3]:  # tags
                    tags = row[3].split(',') if row[3] else []
                    logger.info(f"   标签: {', '.join(tags)}")
                logger.info(f"   地址: {row[6]}")  # address
                logger.info("-" * 50)
                
    finally:
        db.disconnect()


def demo_check_exists():
    """演示检查记录是否存在"""
    logger.info("=== 演示检查记录是否存在 ===")
    
    db = DatabaseManager(**DATABASE_CONFIG)
    if not db.connect():
        return
    
    try:
        # 检查特定医院是否存在
        test_name = "北京康复医院"
        test_phone = "010-88888888"
        
        exists = db.check_hospital_exists(test_name, test_phone)
        if exists:
            logger.info(f"医院 '{test_name}' (电话: {test_phone}) 已存在")
        else:
            logger.info(f"医院 '{test_name}' (电话: {test_phone}) 不存在")
            
        # 只检查名称
        exists_name_only = db.check_hospital_exists(test_name)
        if exists_name_only:
            logger.info(f"名称为 '{test_name}' 的医院已存在")
        else:
            logger.info(f"名称为 '{test_name}' 的医院不存在")
            
    finally:
        db.disconnect()


def main():
    """主函数 - 演示所有基础操作"""
    logger.info("开始演示数据库基础操作...")
    
    # 1. 演示插入单条数据
    demo_insert()
    
    # 2. 演示批量插入数据
    demo_batch_insert()
    
    # 3. 演示查询数据
    demo_query()
    
    # 4. 演示搜索功能
    demo_search()
    
    # 5. 演示检查记录是否存在
    demo_check_exists()
    
    logger.info("数据库基础操作演示完成！")


if __name__ == "__main__":
    main()
