#!/usr/bin/env python3
"""
运行所有ORM框架的Demo
比较不同ORM框架的使用方式和特点
"""

import logging
import sys
import traceback

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_demo(demo_name: str, demo_func):
    """运行单个demo"""
    print("\n" + "="*80)
    print(f"开始运行 {demo_name}")
    print("="*80)
    
    try:
        demo_func()
        print(f"\n✅ {demo_name} 运行成功!")
    except Exception as e:
        print(f"\n❌ {demo_name} 运行失败: {str(e)}")
        print("错误详情:")
        traceback.print_exc()
    
    print("="*80)


def main():
    """主函数"""
    print("🚀 开始运行所有ORM框架Demo")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    print("请确保MySQL数据库正在运行，且hospital表已创建")
    
    input("\n按回车键继续...")
    
    # 1. SQLAlchemy Demo
    try:
        from demo_sqlalchemy import demo_sqlalchemy
        run_demo("SQLAlchemy ORM Demo", demo_sqlalchemy)
    except ImportError as e:
        print(f"❌ 无法导入SQLAlchemy Demo: {e}")
        print("请安装: pip install sqlalchemy pymysql")
    
    # 2. Peewee Demo
    try:
        from demo_peewee import demo_peewee
        run_demo("Peewee ORM Demo", demo_peewee)
    except ImportError as e:
        print(f"❌ 无法导入Peewee Demo: {e}")
        print("请安装: pip install peewee pymysql")
    
    # 3. Tortoise ORM Demo
    try:
        from demo_tortoise import run_demo as run_tortoise_demo
        run_demo("Tortoise ORM Demo", run_tortoise_demo)
    except ImportError as e:
        print(f"❌ 无法导入Tortoise Demo: {e}")
        print("请安装: pip install tortoise-orm pymysql")
    
    # 4. Django ORM Demo
    try:
        from demo_django import demo_django
        run_demo("Django ORM Demo", demo_django)
    except ImportError as e:
        print(f"❌ 无法导入Django Demo: {e}")
        print("请安装: pip install django mysqlclient")
    
    print("\n🎉 所有Demo运行完成!")
    print("\n📊 ORM框架对比总结:")
    print("""
    1. SQLAlchemy:
       ✅ 功能最全面，支持复杂查询
       ✅ 业界标准，大型项目首选
       ❌ 学习曲线稍陡
       ❌ 代码相对冗长
    
    2. Peewee:
       ✅ 简单易用，API直观
       ✅ 学习成本低
       ✅ 代码简洁
       ❌ 功能相对有限
    
    3. Tortoise ORM:
       ✅ 异步支持，性能优秀
       ✅ 适合FastAPI等异步框架
       ✅ API类似Django ORM
       ❌ 相对较新，生态不如SQLAlchemy
    
    4. Django ORM:
       ✅ API优雅，功能强大
       ✅ 文档完善
       ❌ 通常与Django框架绑定
       ❌ 独立使用配置复杂
    
    推荐选择:
    - 大型项目/复杂查询: SQLAlchemy
    - 简单项目/快速开发: Peewee
    - 异步项目/FastAPI: Tortoise ORM
    - Django项目: Django ORM
    """)


if __name__ == "__main__":
    main()
