#!/usr/bin/env python3
"""
Django ORM Demo (独立使用)
Django的ORM可以独立于Django框架使用，功能强大，API优雅
"""

import os
import django
from django.conf import settings
from django.db import models
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置Django设置
if not settings.configured:
    settings.configure(
        DATABASES={
            'default': {
                'ENGINE': 'django.db.backends.mysql',
                'NAME': 'kangfu',
                'USER': 'root',
                'PASSWORD': 'root',
                'HOST': 'localhost',
                'PORT': '3306',
                'OPTIONS': {
                    'charset': 'utf8mb4',
                },
            }
        },
        INSTALLED_APPS=[
            '__main__',  # 当前模块
        ],
        USE_TZ=False,  # 不使用时区
    )
    django.setup()


class Hospital(models.Model):
    """医院模型类"""
    id = models.BigIntegerField(primary_key=True, verbose_name='ID')
    name = models.CharField(max_length=100, null=False, verbose_name='名称')
    alias = models.CharField(max_length=50, null=True, blank=True, verbose_name='别名')
    remark = models.TextField(null=True, blank=True, verbose_name='介绍')
    phone = models.CharField(max_length=50, null=True, blank=True, verbose_name='电话')
    address = models.CharField(max_length=150, null=True, blank=True, verbose_name='地址')
    sort = models.IntegerField(default=0, verbose_name='排序')
    hidden = models.IntegerField(default=0, verbose_name='是否隐藏')
    longitude = models.FloatField(default=0, verbose_name='经度')
    latitude = models.FloatField(default=0, verbose_name='纬度')
    tags = models.CharField(max_length=200, null=True, blank=True, verbose_name='标签')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    create_user = models.BigIntegerField(default=1, verbose_name='创建用户')
    update_time = models.DateTimeField(auto_now=True, verbose_name='修改时间')
    update_user = models.BigIntegerField(default=1, verbose_name='修改用户')
    deleted = models.IntegerField(default=0, verbose_name='逻辑删除')
    version = models.IntegerField(default=0, verbose_name='版本号')
    
    class Meta:
        db_table = 'hospital'
        verbose_name = '医院'
        verbose_name_plural = '医院'
    
    def __str__(self):
        return f"Hospital(id={self.id}, name='{self.name}', phone='{self.phone}')"


class HospitalService:
    """医院服务类 - 封装CRUD操作"""
    
    @staticmethod
    def create_hospital(hospital_data: dict) -> Hospital:
        """创建医院"""
        # 处理tags
        tags_str = ','.join(hospital_data.get('tags', [])) if hospital_data.get('tags') else None
        
        hospital = Hospital.objects.create(
            id=int(datetime.now().timestamp() * 1000000),  # 生成ID
            name=hospital_data.get('name', ''),
            alias=hospital_data.get('alias'),
            remark=hospital_data.get('remark'),
            phone=hospital_data.get('phone'),
            address=hospital_data.get('address'),
            tags=tags_str
        )
        
        logger.info(f"创建医院成功: {hospital.name} (ID: {hospital.id})")
        return hospital
    
    @staticmethod
    def get_hospital_by_id(hospital_id: int) -> Hospital:
        """根据ID获取医院"""
        try:
            hospital = Hospital.objects.get(id=hospital_id, deleted=0)
            return hospital
        except Hospital.DoesNotExist:
            return None
    
    @staticmethod
    def get_hospitals(limit: int = 10, offset: int = 0) -> list:
        """分页获取医院列表"""
        hospitals = (Hospital.objects
                    .filter(deleted=0)
                    .order_by('-create_time')[offset:offset+limit])
        return list(hospitals)
    
    @staticmethod
    def search_hospitals(keyword: str) -> list:
        """搜索医院"""
        from django.db.models import Q
        
        hospitals = Hospital.objects.filter(
            Q(name__icontains=keyword) | Q(alias__icontains=keyword),
            deleted=0
        )
        return list(hospitals)
    
    @staticmethod
    def update_hospital(hospital_id: int, update_data: dict) -> Hospital:
        """更新医院信息"""
        hospital = HospitalService.get_hospital_by_id(hospital_id)
        if not hospital:
            return None
        
        # 处理tags
        if 'tags' in update_data and isinstance(update_data['tags'], list):
            update_data['tags'] = ','.join(update_data['tags']) if update_data['tags'] else None
        
        # 更新字段
        for key, value in update_data.items():
            if hasattr(hospital, key):
                setattr(hospital, key, value)
        
        hospital.save()
        logger.info(f"更新医院成功: {hospital.name}")
        return hospital
    
    @staticmethod
    def delete_hospital(hospital_id: int) -> bool:
        """逻辑删除医院"""
        hospital = HospitalService.get_hospital_by_id(hospital_id)
        if not hospital:
            return False
        
        hospital.deleted = 1
        hospital.save()
        
        logger.info(f"删除医院成功: {hospital.name}")
        return True
    
    @staticmethod
    def check_hospital_exists(name: str, phone: str = None) -> bool:
        """检查医院是否存在"""
        query = Hospital.objects.filter(name=name, deleted=0)
        
        if phone:
            query = query.filter(phone=phone)
        
        return query.exists()
    
    @staticmethod
    def get_hospital_count() -> int:
        """获取医院总数"""
        return Hospital.objects.filter(deleted=0).count()


def demo_django():
    """Django ORM CRUD 演示"""
    logger.info("=== Django ORM Demo ===")
    
    # 1. 创建医院
    logger.info("1. 创建医院")
    hospital_data = {
        'name': 'Django康复医院',
        'alias': 'DJ医院',
        'remark': '使用Django ORM创建的医院',
        'phone': '010-44444444',
        'address': '北京市朝阳区Django大街101号',
        'tags': ['康复科', 'Django', 'ORM']
    }
    
    if not HospitalService.check_hospital_exists(hospital_data['name'], hospital_data['phone']):
        hospital = HospitalService.create_hospital(hospital_data)
        logger.info(f"创建的医院: {hospital}")
    else:
        logger.info("医院已存在，跳过创建")
    
    # 2. 查询医院
    logger.info("\n2. 查询医院")
    hospitals = HospitalService.get_hospitals(limit=3)
    logger.info(f"查询到 {len(hospitals)} 个医院:")
    for h in hospitals:
        tags = h.tags.split(',') if h.tags else []
        logger.info(f"  - {h.name} | {h.phone} | 标签: {tags}")
    
    # 3. 搜索医院
    logger.info("\n3. 搜索医院")
    search_results = HospitalService.search_hospitals('Django')
    logger.info(f"搜索'Django'结果: {len(search_results)} 个")
    for h in search_results:
        logger.info(f"  - {h.name} | {h.phone}")
    
    # 4. 更新医院
    logger.info("\n4. 更新医院")
    if search_results:
        hospital = search_results[0]
        update_data = {
            'remark': '已更新的医院介绍 - Django ORM很优雅！',
            'tags': ['康复科', 'Django', 'ORM', '已更新']
        }
        updated_hospital = HospitalService.update_hospital(hospital.id, update_data)
        if updated_hospital:
            logger.info(f"更新成功: {updated_hospital.remark}")
    
    # 5. 统计信息
    logger.info("\n5. 统计信息")
    total_count = HospitalService.get_hospital_count()
    logger.info(f"数据库中总计 {total_count} 个医院")
    
    logger.info("\nDjango Demo 完成!")


if __name__ == "__main__":
    demo_django()
