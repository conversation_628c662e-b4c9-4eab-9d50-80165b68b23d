#!/usr/bin/env python3
"""
Tortoise ORM Demo
异步Python ORM框架，专为异步设计，性能优秀，适合FastAPI
"""

from tortoise.models import Model
from tortoise.fields import BigIntField, Char<PERSON><PERSON>, TextField, IntField, DatetimeField, FloatField
from tortoise import Tortoise, run_async
from datetime import datetime
import logging
import asyncio

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置
DATABASE_URL = "mysql://root:root@localhost:3306/kangfu"


class Hospital(Model):
    """医院模型类"""
    id = BigIntField(pk=True)
    name = CharField(max_length=100, null=False, description='名称')
    alias = CharField(max_length=50, null=True, description='别名')
    remark = TextField(null=True, description='介绍')
    phone = CharField(max_length=50, null=True, description='电话')
    address = CharField(max_length=150, null=True, description='地址')
    sort = IntField(default=0, description='排序')
    hidden = IntField(default=0, description='是否隐藏')
    longitude = FloatField(default=0, description='经度')
    latitude = FloatField(default=0, description='纬度')
    tags = CharField(max_length=200, null=True, description='标签')
    create_time = DatetimeField(auto_now_add=True, description='创建时间')
    create_user = BigIntField(default=1, description='创建用户')
    update_time = DatetimeField(auto_now=True, description='修改时间')
    update_user = BigIntField(default=1, description='修改用户')
    deleted = IntField(default=0, description='逻辑删除')
    version = IntField(default=0, description='版本号')
    
    class Meta:
        table = "hospital"
    
    def __str__(self):
        return f"Hospital(id={self.id}, name='{self.name}', phone='{self.phone}')"


class HospitalService:
    """医院服务类 - 封装异步CRUD操作"""
    
    @staticmethod
    async def create_hospital(hospital_data: dict) -> Hospital:
        """创建医院"""
        # 处理tags
        tags_str = ','.join(hospital_data.get('tags', [])) if hospital_data.get('tags') else None
        
        hospital = await Hospital.create(
            id=int(datetime.now().timestamp() * 1000000),  # 生成ID
            name=hospital_data.get('name', ''),
            alias=hospital_data.get('alias'),
            remark=hospital_data.get('remark'),
            phone=hospital_data.get('phone'),
            address=hospital_data.get('address'),
            tags=tags_str
        )
        
        logger.info(f"创建医院成功: {hospital.name} (ID: {hospital.id})")
        return hospital
    
    @staticmethod
    async def get_hospital_by_id(hospital_id: int) -> Hospital:
        """根据ID获取医院"""
        hospital = await Hospital.filter(
            id=hospital_id,
            deleted=0
        ).first()
        return hospital
    
    @staticmethod
    async def get_hospitals(limit: int = 10, offset: int = 0) -> list:
        """分页获取医院列表"""
        hospitals = await Hospital.filter(
            deleted=0
        ).order_by('-create_time').limit(limit).offset(offset).all()
        return hospitals
    
    @staticmethod
    async def search_hospitals(keyword: str) -> list:
        """搜索医院"""
        hospitals = await Hospital.filter(
            deleted=0
        ).filter(
            name__icontains=keyword
        ).all()
        
        # 也搜索别名
        alias_results = await Hospital.filter(
            deleted=0,
            alias__icontains=keyword
        ).all()
        
        # 合并结果并去重
        all_results = hospitals + alias_results
        unique_results = list({h.id: h for h in all_results}.values())
        
        return unique_results
    
    @staticmethod
    async def update_hospital(hospital_id: int, update_data: dict) -> Hospital:
        """更新医院信息"""
        hospital = await HospitalService.get_hospital_by_id(hospital_id)
        if not hospital:
            return None
        
        # 处理tags
        if 'tags' in update_data and isinstance(update_data['tags'], list):
            update_data['tags'] = ','.join(update_data['tags']) if update_data['tags'] else None
        
        # 更新字段
        for key, value in update_data.items():
            if hasattr(hospital, key):
                setattr(hospital, key, value)
        
        await hospital.save()
        logger.info(f"更新医院成功: {hospital.name}")
        return hospital
    
    @staticmethod
    async def delete_hospital(hospital_id: int) -> bool:
        """逻辑删除医院"""
        hospital = await HospitalService.get_hospital_by_id(hospital_id)
        if not hospital:
            return False
        
        hospital.deleted = 1
        await hospital.save()
        
        logger.info(f"删除医院成功: {hospital.name}")
        return True
    
    @staticmethod
    async def check_hospital_exists(name: str, phone: str = None) -> bool:
        """检查医院是否存在"""
        query = Hospital.filter(name=name, deleted=0)
        
        if phone:
            query = query.filter(phone=phone)
        
        hospital = await query.first()
        return hospital is not None
    
    @staticmethod
    async def get_hospital_count() -> int:
        """获取医院总数"""
        return await Hospital.filter(deleted=0).count()


async def demo_tortoise():
    """Tortoise ORM CRUD 演示"""
    logger.info("=== Tortoise ORM Demo ===")
    
    # 初始化数据库连接
    await Tortoise.init(
        db_url=DATABASE_URL,
        modules={'models': ['__main__']}  # 当前模块
    )
    
    try:
        # 1. 创建医院
        logger.info("1. 创建医院")
        hospital_data = {
            'name': 'Tortoise康复医院',
            'alias': 'TO医院',
            'remark': '使用Tortoise ORM创建的医院',
            'phone': '010-33333333',
            'address': '北京市朝阳区Tortoise大街789号',
            'tags': ['康复科', 'Tortoise', 'AsyncORM']
        }
        
        if not await HospitalService.check_hospital_exists(hospital_data['name'], hospital_data['phone']):
            hospital = await HospitalService.create_hospital(hospital_data)
            logger.info(f"创建的医院: {hospital}")
        else:
            logger.info("医院已存在，跳过创建")
        
        # 2. 查询医院
        logger.info("\n2. 查询医院")
        hospitals = await HospitalService.get_hospitals(limit=3)
        logger.info(f"查询到 {len(hospitals)} 个医院:")
        for h in hospitals:
            tags = h.tags.split(',') if h.tags else []
            logger.info(f"  - {h.name} | {h.phone} | 标签: {tags}")
        
        # 3. 搜索医院
        logger.info("\n3. 搜索医院")
        search_results = await HospitalService.search_hospitals('Tortoise')
        logger.info(f"搜索'Tortoise'结果: {len(search_results)} 个")
        for h in search_results:
            logger.info(f"  - {h.name} | {h.phone}")
        
        # 4. 更新医院
        logger.info("\n4. 更新医院")
        if search_results:
            hospital = search_results[0]
            update_data = {
                'remark': '已更新的医院介绍 - Tortoise异步很快！',
                'tags': ['康复科', 'Tortoise', 'AsyncORM', '已更新']
            }
            updated_hospital = await HospitalService.update_hospital(hospital.id, update_data)
            if updated_hospital:
                logger.info(f"更新成功: {updated_hospital.remark}")
        
        # 5. 统计信息
        logger.info("\n5. 统计信息")
        total_count = await HospitalService.get_hospital_count()
        logger.info(f"数据库中总计 {total_count} 个医院")
        
        logger.info("\nTortoise Demo 完成!")
        
    finally:
        await Tortoise.close_connections()


def run_demo():
    """运行异步demo"""
    run_async(demo_tortoise())


if __name__ == "__main__":
    run_demo()
