#!/usr/bin/env python3
"""
数据库初始化脚本
用于创建数据库和表结构
"""

import pymysql
import sys
from config import DATABASE_CONFIG
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_database():
    """创建数据库"""
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=DATABASE_CONFIG['host'],
            port=DATABASE_CONFIG['port'],
            user=DATABASE_CONFIG['user'],
            password=DATABASE_CONFIG['password'],
            charset=DATABASE_CONFIG['charset']
        )
        
        with connection.cursor() as cursor:
            # 创建数据库
            create_db_sql = f"CREATE DATABASE IF NOT EXISTS {DATABASE_CONFIG['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
            cursor.execute(create_db_sql)
            logger.info(f"数据库 {DATABASE_CONFIG['database']} 创建成功")
        
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"创建数据库失败: {str(e)}")
        return False


def create_tables():
    """创建数据表"""
    try:
        # 连接到指定数据库
        connection = pymysql.connect(
            host=DATABASE_CONFIG['host'],
            port=DATABASE_CONFIG['port'],
            user=DATABASE_CONFIG['user'],
            password=DATABASE_CONFIG['password'],
            database=DATABASE_CONFIG['database'],
            charset=DATABASE_CONFIG['charset']
        )
        
        with connection.cursor() as cursor:
            # 创建医院信息表
            create_hospitals_table = """
            CREATE TABLE IF NOT EXISTS hospitals (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                name VARCHAR(255) NOT NULL COMMENT '医院名称',
                alias VARCHAR(255) DEFAULT '' COMMENT '医院别名',
                tags JSON COMMENT '医院标签（JSON格式）',
                remark TEXT COMMENT '医院介绍/备注',
                phone VARCHAR(100) DEFAULT '' COMMENT '联系电话',
                address TEXT COMMENT '医院地址',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_name (name),
                INDEX idx_phone (phone),
                INDEX idx_created_at (created_at),
                UNIQUE KEY uk_name_phone (name, phone)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医院信息表';
            """
            
            cursor.execute(create_hospitals_table)
            logger.info("医院信息表创建成功")
            
            # 创建爬取日志表
            create_spider_log_table = """
            CREATE TABLE IF NOT EXISTS spider_logs (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                page_num INT NOT NULL COMMENT '页面编号',
                total_found INT DEFAULT 0 COMMENT '发现的医院总数',
                success_count INT DEFAULT 0 COMMENT '成功保存的数量',
                failed_count INT DEFAULT 0 COMMENT '失败的数量',
                start_time TIMESTAMP NULL COMMENT '开始时间',
                end_time TIMESTAMP NULL COMMENT '结束时间',
                status ENUM('running', 'completed', 'failed') DEFAULT 'running' COMMENT '状态',
                error_message TEXT COMMENT '错误信息',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                INDEX idx_page_num (page_num),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='爬取日志表';
            """
            
            cursor.execute(create_spider_log_table)
            logger.info("爬取日志表创建成功")
        
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"创建数据表失败: {str(e)}")
        return False


def test_connection():
    """测试数据库连接"""
    try:
        connection = pymysql.connect(**DATABASE_CONFIG)
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            logger.info(f"MySQL版本: {version[0]}")
            
            cursor.execute("SELECT DATABASE()")
            database = cursor.fetchone()
            logger.info(f"当前数据库: {database[0]}")
            
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            logger.info(f"数据库中的表: {[table[0] for table in tables]}")
        
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"测试数据库连接失败: {str(e)}")
        return False


def main():
    """主函数"""
    logger.info("开始初始化数据库...")
    
    # 检查配置
    if DATABASE_CONFIG['password'] == 'your_password':
        logger.error("请先在 config.py 中配置正确的数据库密码！")
        sys.exit(1)
    
    # 创建数据库
    if not create_database():
        logger.error("数据库创建失败，程序退出")
        sys.exit(1)
    
    # 创建表
    if not create_tables():
        logger.error("数据表创建失败，程序退出")
        sys.exit(1)
    
    # 测试连接
    if not test_connection():
        logger.error("数据库连接测试失败，程序退出")
        sys.exit(1)
    
    logger.info("数据库初始化完成！")
    logger.info("现在可以运行爬虫脚本了：python spider_with_db.py")


if __name__ == "__main__":
    main()
