#!/usr/bin/env python3
"""
数据库初始化脚本
用于创建数据库和表结构
"""

import pymysql
import sys
from config import DATABASE_CONFIG
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_database():
    """创建数据库"""
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=DATABASE_CONFIG['host'],
            port=DATABASE_CONFIG['port'],
            user=DATABASE_CONFIG['user'],
            password=DATABASE_CONFIG['password'],
            charset=DATABASE_CONFIG['charset']
        )
        
        with connection.cursor() as cursor:
            # 创建数据库
            create_db_sql = f"CREATE DATABASE IF NOT EXISTS {DATABASE_CONFIG['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci"
            cursor.execute(create_db_sql)
            logger.info(f"数据库 {DATABASE_CONFIG['database']} 创建成功")
        
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"创建数据库失败: {str(e)}")
        return False


def create_tables():
    """创建数据表"""
    try:
        # 连接到指定数据库
        connection = pymysql.connect(
            host=DATABASE_CONFIG['host'],
            port=DATABASE_CONFIG['port'],
            user=DATABASE_CONFIG['user'],
            password=DATABASE_CONFIG['password'],
            database=DATABASE_CONFIG['database'],
            charset=DATABASE_CONFIG['charset']
        )
        
        with connection.cursor() as cursor:
            # 创建医院信息表（使用用户提供的表结构）
            create_hospital_table = """
            CREATE TABLE IF NOT EXISTS `hospital` (
                `id` BIGINT NOT NULL COMMENT 'ID',
                `name` VARCHAR (100) NOT NULL COMMENT '名称',
                `alias` VARCHAR (50) NULL DEFAULT NULL COMMENT '别名',
                `remark` TEXT DEFAULT NULL COMMENT '介绍',
                `phone` VARCHAR (50) NULL DEFAULT NULL COMMENT '电话',
                `address` VARCHAR (150) NULL DEFAULT NULL COMMENT '地址',
                `sort` INT NOT NULL DEFAULT '0' COMMENT '排序',
                `hidden` TINYINT NOT NULL DEFAULT '0' COMMENT '是否隐藏（0、否；1、是）',
                `longitude` DOUBLE NOT NULL DEFAULT 0 COMMENT '经度',
                `latitude` DOUBLE NOT NULL DEFAULT 0 COMMENT '纬度',
                `tags` VARCHAR (200) NULL DEFAULT NULL COMMENT '标签（多个逗号分隔）',
                `create_time` DATETIME NOT NULL COMMENT '创建时间',
                `create_user` BIGINT NOT NULL COMMENT '创建用户',
                `update_time` DATETIME NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                `update_user` BIGINT NOT NULL DEFAULT '0' COMMENT '修改用户',
                `deleted` INT NOT NULL DEFAULT '0' COMMENT '逻辑删除（0代表未删除，非0代表已删除）',
                `version` INT NOT NULL DEFAULT '0' COMMENT '更新版本号（乐观锁）',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医院信息';
            """

            cursor.execute(create_hospital_table)
            logger.info("医院信息表创建成功")
            
            # 创建爬取日志表
            create_spider_log_table = """
            CREATE TABLE IF NOT EXISTS spider_logs (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                page_num INT NOT NULL COMMENT '页面编号',
                total_found INT DEFAULT 0 COMMENT '发现的医院总数',
                success_count INT DEFAULT 0 COMMENT '成功保存的数量',
                failed_count INT DEFAULT 0 COMMENT '失败的数量',
                start_time TIMESTAMP NULL COMMENT '开始时间',
                end_time TIMESTAMP NULL COMMENT '结束时间',
                status ENUM('running', 'completed', 'failed') DEFAULT 'running' COMMENT '状态',
                error_message TEXT COMMENT '错误信息',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                INDEX idx_page_num (page_num),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='爬取日志表';
            """
            
            cursor.execute(create_spider_log_table)
            logger.info("爬取日志表创建成功")
        
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"创建数据表失败: {str(e)}")
        return False


def test_connection():
    """测试数据库连接"""
    try:
        connection = pymysql.connect(**DATABASE_CONFIG)
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            logger.info(f"MySQL版本: {version[0]}")
            
            cursor.execute("SELECT DATABASE()")
            database = cursor.fetchone()
            logger.info(f"当前数据库: {database[0]}")
            
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            logger.info(f"数据库中的表: {[table[0] for table in tables]}")
        
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"测试数据库连接失败: {str(e)}")
        return False


def main():
    """主函数"""
    logger.info("开始初始化数据库...")
    
    # 检查配置（密码已经配置为root，无需检查）
    logger.info(f"使用数据库配置: {DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}")
    
    # 创建数据库
    if not create_database():
        logger.error("数据库创建失败，程序退出")
        sys.exit(1)
    
    # 创建表
    if not create_tables():
        logger.error("数据表创建失败，程序退出")
        sys.exit(1)
    
    # 测试连接
    if not test_connection():
        logger.error("数据库连接测试失败，程序退出")
        sys.exit(1)
    
    logger.info("数据库初始化完成！")
    logger.info("现在可以运行爬虫脚本了：python spider_with_db.py")


if __name__ == "__main__":
    main()
