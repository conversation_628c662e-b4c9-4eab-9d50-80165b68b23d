import re
import requests
import time
from bs4 import BeautifulSoup


def clean_phone(text):
    # 去除"预约电话："等标签文本
    text = re.sub(r'预约电话：|联系电话：', '', text)
    # 去除空白字符(空格、换行符、制表符等)
    text = re.sub(r'\s+', '', text)
    return text


def clean_address(text):
    # 去除"联系地址："等标签文本
    text = re.sub(r'联系地址：|地址：', '', text)
    # 去除空白字符(空格、换行符、制表符等)
    text = re.sub(r'\s+', ' ', text)
    return text.strip()


def extract_detail_data(detail_url, headers):
    """提取详情页数据"""
    try:
        detail_response = requests.get(detail_url, headers=headers)
        detail_response.encoding = 'utf-8'

        if detail_response.status_code == 200:
            detail_soup = BeautifulSoup(detail_response.text, 'html.parser')

            # 提取各个字段的数据
            hospital_data = {}

            # 名称的css选择器（1个元素）
            name_element = detail_soup.select_one('body > div.wrapper_box > div.wrap_box > div.wrap_left > div.wrap_title > b')
            hospital_data['name'] = name_element.get_text(strip=True) if name_element else ''

            # 标签的css选择器1（多个元素）- 全部要
            tags1 = []
            tags1_elements = detail_soup.select('body > div.wrapper_box > div.wrap_box > div.wrap_left > div.wrap_title > span')
            if tags1_elements:
                tags1 = [tag.get_text(strip=True) for tag in tags1_elements if tag.get_text(strip=True)]

            # 标签的css选择器2（多个元素）- 只要包含"康复"的
            tags2 = []
            tags2_elements = detail_soup.select('body > div.wrapper_box > div.wrapper > div.left_box > div:nth-child(1) > div.left_ks > dl > dd > a')
            if tags2_elements:
                tags2 = [tag.get_text(strip=True) for tag in tags2_elements
                         if tag.get_text(strip=True) and '康复' in tag.get_text(strip=True)]

            # 合并所有标签
            all_tags = tags1 + tags2
            hospital_data['tags'] = all_tags

            # 别名的css选择器（1个元素）
            alias_element = detail_soup.select_one('body > div.wrapper_box > div.wrap_box > div.wrap_left > div.wrap_cont > div.wrap_text > p:nth-child(1) > span')
            hospital_data['alias'] = alias_element.get_text(strip=True) if alias_element else ''

            # 介绍的css选择器（1个元素）
            intro_element = detail_soup.select_one('body > div.wrapper_box > div.wrap_box > div.wrap_left > div.wrap_cont > div.wrap_text > p:nth-child(2) > span > a')
            # hospital_data['remark'] = intro_element.get_text(strip=True) if intro_element else ''
            hospital_data['remark'] = ' https://yyk.99.com.cn' + intro_element.get('href', '') if intro_element else ''

            # 电话的css选择器（1个元素）
            phone_element = detail_soup.select_one('body > div.wrapper_box > div.wrap_box > div.wrap_left > div.wrap_cont > div.wrap_text > p:nth-child(3) > span')
            raw_phone = phone_element.get_text(strip=True) if phone_element else ''
            hospital_data['phone'] = clean_phone(raw_phone)

            # 地址的css选择器（1个元素）
            address_element = detail_soup.select_one('body > div.wrapper_box > div.wrap_box > div.wrap_left > div.wrap_cont > div.wrap_text > p:nth-child(4) > span')
            raw_address = address_element.get_text(strip=True) if address_element else ''
            hospital_data['address'] = clean_address(raw_address)

            return hospital_data
        else:
            print(f"获取详情页面失败. 状态码: {detail_response.status_code}")
            return None

    except Exception as e:
        print(f"提取详情数据时发生错误: {str(e)}")
        return None


def fetch_page_data(url, page_num, headers):
    """获取单页医院列表数据并抓取详情"""
    try:
        # 获取网页内容
        response = requests.get(url, headers=headers)
        response.encoding = 'utf-8'

        if response.status_code == 200:
            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找指定路径下的所有dl元素
            dl_elements = soup.select('body > .wrapper > .wrap-box > .yy-cont > dl')

            if dl_elements:
                print(f"第 {page_num} 页找到 {len(dl_elements)} 个医院")

                for i, dl in enumerate(dl_elements, 1):
                    try:
                        # 提取列表页的基本信息
                        title_element = dl.find('dd').find('b').find('a')
                        if not title_element:
                            continue

                        detail_url = title_element.get('href', '')
                        list_title = title_element.get_text(strip=True)

                        print(f"\n{'=' * 60}")
                        print(f"第 {page_num} 页 - 正在处理第 {i} 个医院: {list_title}")
                        print(f"详情链接: {detail_url}")

                        # 抓取详情页数据
                        detail_data = extract_detail_data(detail_url, headers)

                        if detail_data:
                            print(f"医院名称: {detail_data['name']}")
                            print(f"别名: {detail_data['alias']}")
                            print(f"标签: {', '.join(detail_data['tags'])}")
                            print(f"介绍: {detail_data['remark']}")
                            print(f"电话: {detail_data['phone']}")
                            print(f"地址: {detail_data['address']}")
                        else:
                            print("详情数据提取失败")

                        # 添加延时，避免请求过于频繁
                        time.sleep(1)

                    except Exception as e:
                        print(f"处理第 {page_num} 页第 {i} 个医院时发生错误: {str(e)}")
                        continue

                return len(dl_elements)
            else:
                print(f"第 {page_num} 页未找到医院数据")
                return 0

        else:
            print(f"获取第 {page_num} 页失败. 状态码: {response.status_code}")
            return 0

    except Exception as e:
        print(f"处理第 {page_num} 页时发生错误: {str(e)}")
        return 0


def fetch_hospital_data(base_url, start_page=1, end_page=60):
    """获取医院列表数据并抓取详情 - 支持多页爬取"""
    # 添加请求头,模拟浏览器访问
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    total_hospitals = 0

    print(f"开始爬取第 {start_page} 页到第 {end_page} 页的数据...")

    for page in range(start_page, end_page + 1):
        try:
            # 构建当前页面的URL
            current_url = base_url.replace('p=1', f'p={page}')
            print(f"\n{'#' * 80}")
            print(f"正在爬取第 {page} 页: {current_url}")
            print(f"{'#' * 80}")

            # 获取当前页面的数据
            page_hospitals = fetch_page_data(current_url, page, headers)
            total_hospitals += page_hospitals

            # 如果当前页面没有数据，可能已经到了最后一页
            if page_hospitals == 0:
                print(f"第 {page} 页没有数据，可能已经到达最后一页")
                break

            # 页面间添加延时
            print(f"第 {page} 页处理完成，等待2秒后继续...")
            time.sleep(2)

        except Exception as e:
            print(f"处理第 {page} 页时发生错误: {str(e)}")
            continue

    print(f"\n{'*' * 80}")
    print(f"爬取完成！总共处理了 {total_hospitals} 个医院")
    print(f"{'*' * 80}")


if __name__ == "__main__":
    base_url = "https://so.99.com.cn/search.php?s=relevance&proj=yyk&f=_all&q=康复医院&p=1"

    # 爬取第1页到第60页
    # 如果只想爬取部分页面，可以修改start_page和end_page参数
    # 例如：fetch_hospital_data(base_url, start_page=1, end_page=5) 只爬取前5页
    fetch_hospital_data(base_url, start_page=1, end_page=60)
