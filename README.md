# 康复医院数据爬取项目

这是一个基于FastAPI的康复医院数据爬取项目，可以从99健康网爬取康复医院信息并保存到MySQL数据库中。

## 项目结构

```
kangfu/
├── main.py              # FastAPI主应用
├── spider.py            # 原始爬虫脚本（仅打印数据）
├── spider_with_db.py    # 集成数据库的爬虫脚本
├── database.py          # 数据库管理类
├── config.py            # 配置文件
├── init_db.py           # 数据库初始化脚本
├── view_data.py         # 数据查看工具
├── test_db.py           # 数据库测试脚本
├── requirements.txt     # 依赖包列表
└── README.md           # 项目说明文档
```

## 功能特性

- 🕷️ 爬取99健康网的康复医院数据
- 💾 自动保存数据到MySQL数据库
- 🔍 支持重复数据检测，避免重复插入
- 📊 提供数据统计和查看功能
- 🛠️ 完整的数据库管理工具
- 📝 详细的日志记录

## 数据字段

每个医院记录包含以下字段：
- `id`: 主键ID（BIGINT）
- `name`: 医院名称
- `alias`: 医院别名
- `remark`: 医院介绍/备注
- `phone`: 联系电话
- `address`: 医院地址
- `tags`: 医院标签（逗号分隔的字符串）
- `sort`: 排序字段
- `hidden`: 是否隐藏
- `longitude`: 经度
- `latitude`: 纬度
- `create_time`: 创建时间
- `create_user`: 创建用户
- `update_time`: 更新时间
- `update_user`: 更新用户
- `deleted`: 逻辑删除标记
- `version`: 版本号（乐观锁）

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置数据库

数据库配置已经在 `config.py` 中设置好了：

```python
DATABASE_CONFIG = {
    'host': 'localhost',        # 数据库主机地址
    'port': 3306,              # 数据库端口
    'user': 'root',            # 数据库用户名
    'password': 'root',        # 数据库密码
    'database': 'kangfu',      # 数据库名称
    'charset': 'utf8mb4'       # 字符集
}
```

如果你的数据库配置不同，请修改 `config.py` 文件。

### 3. 测试数据库连接

首先测试数据库连接是否正常：

```bash
python test_db.py
```

这个脚本会：
- 测试数据库连接
- 检查表结构
- 插入测试数据
- 验证数据查询功能

### 4. 初始化数据库（可选）

如果需要创建数据库和表，运行：

```bash
python init_db.py
```

这个脚本会：
- 创建数据库（如果不存在）
- 创建必要的数据表
- 测试数据库连接

## 使用方法

### 1. 运行爬虫（保存到数据库）

```bash
python spider_with_db.py
```

这个脚本会：
- 爬取指定页面范围的医院数据
- 自动保存到MySQL数据库
- 跳过已存在的重复数据
- 显示详细的进度信息

### 2. 查看数据

```bash
python view_data.py
```

数据查看工具提供以下功能：
- 显示数据统计信息
- 查看最近添加的医院
- 按名称搜索医院
- 显示标签统计

### 3. 运行FastAPI应用

```bash
uvicorn main:app --reload
```

然后访问 http://localhost:8000 查看API文档。

## 配置选项

在 `config.py` 中可以调整以下配置：

### 爬虫配置
```python
SPIDER_CONFIG = {
    'base_url': "https://so.99.com.cn/search.php?s=relevance&proj=yyk&f=_all&q=康复医院&p=1",
    'request_delay': 1,        # 请求间隔（秒）
    'page_delay': 2,          # 页面间隔（秒）
    'start_page': 1,          # 开始页面
    'end_page': 60,           # 结束页面
    'timeout': 30             # 请求超时时间（秒）
}
```

### 日志配置
```python
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'filename': 'spider.log'  # 日志文件名，如果为None则输出到控制台
}
```

## 数据库表结构

### hospital 表
```sql
CREATE TABLE `hospital` (
    `id` BIGINT NOT NULL COMMENT 'ID',
    `name` VARCHAR (100) NOT NULL COMMENT '名称',
    `alias` VARCHAR (50) NULL DEFAULT NULL COMMENT '别名',
    `remark` TEXT DEFAULT NULL COMMENT '介绍',
    `phone` VARCHAR (50) NULL DEFAULT NULL COMMENT '电话',
    `address` VARCHAR (150) NULL DEFAULT NULL COMMENT '地址',
    `sort` INT NOT NULL DEFAULT '0' COMMENT '排序',
    `hidden` TINYINT NOT NULL DEFAULT '0' COMMENT '是否隐藏（0、否；1、是）',
    `longitude` DOUBLE NOT NULL DEFAULT 0 COMMENT '经度',
    `latitude` DOUBLE NOT NULL DEFAULT 0 COMMENT '纬度',
    `tags` VARCHAR (200) NULL DEFAULT NULL COMMENT '标签（多个逗号分隔）',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    `create_user` BIGINT NOT NULL COMMENT '创建用户',
    `update_time` DATETIME NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `update_user` BIGINT NOT NULL DEFAULT '0' COMMENT '修改用户',
    `deleted` INT NOT NULL DEFAULT '0' COMMENT '逻辑删除（0代表未删除，非0代表已删除）',
    `version` INT NOT NULL DEFAULT '0' COMMENT '更新版本号（乐观锁）',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医院信息';
```

## 注意事项

1. **请求频率**: 脚本已设置合理的请求间隔，避免对目标网站造成过大压力
2. **数据去重**: 基于医院名称和电话号码进行去重
3. **错误处理**: 包含完善的异常处理机制
4. **日志记录**: 详细记录爬取过程和错误信息
5. **数据完整性**: 使用事务确保数据一致性

## 常见问题

### Q: 数据库连接失败
A: 请检查 `config.py` 中的数据库配置信息，确保MySQL服务正在运行。

### Q: 爬取速度慢
A: 可以在 `config.py` 中调整 `request_delay` 和 `page_delay` 参数，但不建议设置过小的值。

### Q: 部分数据缺失
A: 这是正常现象，因为源网站的数据本身可能不完整。

### Q: 如何只爬取特定页面
A: 修改 `spider_with_db.py` 中的 `start_page` 和 `end_page` 参数。

## 开发和扩展

如果需要扩展功能，可以：
1. 修改 `extract_detail_data` 函数添加新的数据字段
2. 在数据库表中添加相应的列
3. 更新数据插入逻辑

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的robots.txt和使用条款。
