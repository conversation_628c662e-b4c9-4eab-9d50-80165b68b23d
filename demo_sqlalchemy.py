#!/usr/bin/env python3
"""
SQLAlchemy ORM Demo
最强大的Python ORM框架，功能全面，业界标准
"""

from sqlalchemy import create_engine, Column, BigInteger, String, Text, Integer, DateTime, Double
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库连接配置
DATABASE_URL = "mysql+pymysql://root:root@localhost:3306/kangfu"

# 创建数据库引擎
engine = create_engine(DATABASE_URL, echo=False)  # echo=True 可以看到生成的SQL

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基类
Base = declarative_base()


class Hospital(Base):
    """医院模型类"""
    __tablename__ = 'hospital'
    
    id = Column(BigInteger, primary_key=True, comment='ID')
    name = Column(String(100), nullable=False, comment='名称')
    alias = Column(String(50), nullable=True, comment='别名')
    remark = Column(Text, nullable=True, comment='介绍')
    phone = Column(String(50), nullable=True, comment='电话')
    address = Column(String(150), nullable=True, comment='地址')
    sort = Column(Integer, nullable=False, default=0, comment='排序')
    hidden = Column(Integer, nullable=False, default=0, comment='是否隐藏')
    longitude = Column(Double, nullable=False, default=0, comment='经度')
    latitude = Column(Double, nullable=False, default=0, comment='纬度')
    tags = Column(String(200), nullable=True, comment='标签')
    create_time = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    create_user = Column(BigInteger, nullable=False, default=1, comment='创建用户')
    update_time = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='修改时间')
    update_user = Column(BigInteger, nullable=False, default=1, comment='修改用户')
    deleted = Column(Integer, nullable=False, default=0, comment='逻辑删除')
    version = Column(Integer, nullable=False, default=0, comment='版本号')
    
    def __repr__(self):
        return f"<Hospital(id={self.id}, name='{self.name}', phone='{self.phone}')>"


class HospitalService:
    """医院服务类 - 封装CRUD操作"""
    
    def __init__(self):
        self.session = SessionLocal()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.session.close()
    
    def create_hospital(self, hospital_data: dict) -> Hospital:
        """创建医院"""
        # 处理tags
        tags_str = ','.join(hospital_data.get('tags', [])) if hospital_data.get('tags') else None
        
        hospital = Hospital(
            id=int(datetime.now().timestamp() * 1000000),  # 生成ID
            name=hospital_data.get('name', ''),
            alias=hospital_data.get('alias'),
            remark=hospital_data.get('remark'),
            phone=hospital_data.get('phone'),
            address=hospital_data.get('address'),
            tags=tags_str
        )
        
        self.session.add(hospital)
        self.session.commit()
        self.session.refresh(hospital)
        
        logger.info(f"创建医院成功: {hospital.name} (ID: {hospital.id})")
        return hospital
    
    def get_hospital_by_id(self, hospital_id: int) -> Hospital:
        """根据ID获取医院"""
        hospital = self.session.query(Hospital).filter(
            Hospital.id == hospital_id,
            Hospital.deleted == 0
        ).first()
        return hospital
    
    def get_hospitals(self, limit: int = 10, offset: int = 0) -> list:
        """分页获取医院列表"""
        hospitals = self.session.query(Hospital).filter(
            Hospital.deleted == 0
        ).order_by(Hospital.create_time.desc()).limit(limit).offset(offset).all()
        return hospitals
    
    def search_hospitals(self, keyword: str) -> list:
        """搜索医院"""
        hospitals = self.session.query(Hospital).filter(
            (Hospital.name.like(f'%{keyword}%') | Hospital.alias.like(f'%{keyword}%')),
            Hospital.deleted == 0
        ).all()
        return hospitals
    
    def update_hospital(self, hospital_id: int, update_data: dict) -> Hospital:
        """更新医院信息"""
        hospital = self.get_hospital_by_id(hospital_id)
        if not hospital:
            return None
        
        # 更新字段
        for key, value in update_data.items():
            if key == 'tags' and isinstance(value, list):
                value = ','.join(value) if value else None
            if hasattr(hospital, key):
                setattr(hospital, key, value)
        
        hospital.update_time = datetime.now()
        self.session.commit()
        self.session.refresh(hospital)
        
        logger.info(f"更新医院成功: {hospital.name}")
        return hospital
    
    def delete_hospital(self, hospital_id: int) -> bool:
        """逻辑删除医院"""
        hospital = self.get_hospital_by_id(hospital_id)
        if not hospital:
            return False
        
        hospital.deleted = 1
        hospital.update_time = datetime.now()
        self.session.commit()
        
        logger.info(f"删除医院成功: {hospital.name}")
        return True
    
    def check_hospital_exists(self, name: str, phone: str = None) -> bool:
        """检查医院是否存在"""
        query = self.session.query(Hospital).filter(
            Hospital.name == name,
            Hospital.deleted == 0
        )
        
        if phone:
            query = query.filter(Hospital.phone == phone)
        
        return query.first() is not None
    
    def get_hospital_count(self) -> int:
        """获取医院总数"""
        return self.session.query(Hospital).filter(Hospital.deleted == 0).count()


def demo_sqlalchemy():
    """SQLAlchemy CRUD 演示"""
    logger.info("=== SQLAlchemy ORM Demo ===")
    
    with HospitalService() as service:
        # 1. 创建医院
        logger.info("1. 创建医院")
        hospital_data = {
            'name': 'SQLAlchemy康复医院',
            'alias': 'SA医院',
            'remark': '使用SQLAlchemy ORM创建的医院',
            'phone': '010-11111111',
            'address': '北京市朝阳区SQLAlchemy大街123号',
            'tags': ['康复科', 'SQLAlchemy', 'ORM']
        }
        
        if not service.check_hospital_exists(hospital_data['name'], hospital_data['phone']):
            hospital = service.create_hospital(hospital_data)
            logger.info(f"创建的医院: {hospital}")
        else:
            logger.info("医院已存在，跳过创建")
        
        # 2. 查询医院
        logger.info("\n2. 查询医院")
        hospitals = service.get_hospitals(limit=3)
        logger.info(f"查询到 {len(hospitals)} 个医院:")
        for h in hospitals:
            tags = h.tags.split(',') if h.tags else []
            logger.info(f"  - {h.name} | {h.phone} | 标签: {tags}")
        
        # 3. 搜索医院
        logger.info("\n3. 搜索医院")
        search_results = service.search_hospitals('SQLAlchemy')
        logger.info(f"搜索'SQLAlchemy'结果: {len(search_results)} 个")
        for h in search_results:
            logger.info(f"  - {h.name} | {h.phone}")
        
        # 4. 更新医院
        logger.info("\n4. 更新医院")
        if search_results:
            hospital = search_results[0]
            update_data = {
                'remark': '已更新的医院介绍 - SQLAlchemy很强大！',
                'tags': ['康复科', 'SQLAlchemy', 'ORM', '已更新']
            }
            updated_hospital = service.update_hospital(hospital.id, update_data)
            if updated_hospital:
                logger.info(f"更新成功: {updated_hospital.remark}")
        
        # 5. 统计信息
        logger.info("\n5. 统计信息")
        total_count = service.get_hospital_count()
        logger.info(f"数据库中总计 {total_count} 个医院")
        
        logger.info("\nSQLAlchemy Demo 完成!")


if __name__ == "__main__":
    demo_sqlalchemy()
